Role:
You are genius python developer.

Action:
Build the full project of CLI application loading web page:
The application works as the following description:

Step 1 Retrieve proxies and mail servers lists
-Read the url list for proxies list web site use all the efficient web site you know(like https://free-proxy-list.net,...) from the configuration file.
-Retrieve the lists of proxies by scraping the websites contents {ip}:{port}. you can use scrapy
-Test each proxies retrieved if it is responding and anonymous, generate a text file (proxies.lst) with the validated proxies for used with https and http.
-The proxy availability is carefully tested.

Step2 Load the full web page
- The web page loading use a specialized library able to use custom proxy and user_agents (with  Scrapy or selenium).
- The all content of the web page is downloaded.
- the url of the webpage is passed in parameter to the CLI app.
- The web client use a list of user_agent contains in the agents.lst text file for loading the page.
- The application choose a random user_agent string in the agents.lst file.
- The application use every proxies in the validated list (proxies.lst) for accessing the web page url.

Constraints:
The application process the Step1 before Step2 unless the step1 can be enable and disable in the configuration file.
The application integrates full error handling mechanisms and is displayed on the console.
The application use colors , icons and animations for displaying the status of the application.
The application integrate the display of the number of working proxies in the validated list (proxies.lst).
The application integrate a cache mechanism who can be enable and disable in the configuration file.
A progression gauge is also displayed for visualizing the process regarding of the list of validated proxies in the list.
The Step2 can be threaded, the number of simultaneous threads is set in the configuration file.
The configuration parameters are stored in an env file.

Reflect on a step by step approach for implementing the most efficient source code and project architecture for achieving best performance, efficiency and reliability for long process.