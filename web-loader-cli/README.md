# Web Page Loader CLI 🚀

[![Python Version](https://img.shields.io/badge/python-3.9%2B-blue)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Open Issues](https://img.shields.io/github/issues-raw/:user/:repo)](https://github.com/your-username/web-loader-cli/issues)
[![Stars](https://img.shields.io/github/stars/:user/:repo?style=social)](https://github.com/your-username/web-loader-cli)

A powerful CLI tool to load web pages using validated proxies with support for multiple proxy types (HTTP, HTTPS, SOCKS4, SOCKS5).

## ✨ Features
- Proxy scraping from multiple sources
- Proxy validation with anonymity checks
- Multi-threaded page loading
- Support for HTTP, HTTPS, SOCKS4, and SOCKS5 proxies
- User agent rotation
- Configurable caching of validated proxies
- Rich terminal output with progress tracking

## ⚙️ Requirements
- Python 3.9+
- Pip package manager

## 🚀 Installation
```bash
# Clone the repository
git clone https://github.com/your-username/web-loader-cli.git
cd web-loader-cli

#Creare virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

## 🔧 Configuration
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit the `.env` file with your preferred settings:

```ini
# Enable/disable proxy scraping
ENABLE_PROXY_SCRAPING=True

# Enable/disable caching
ENABLE_CACHE=True
CACHE_DURATION_MINUTES=60

# Proxy sources (comma-separated)
PROXY_URLS="https://free-proxy-list.net/,https://spys.one,..."

# Proxy test timeout (seconds)
PROXY_TEST_TIMEOUT=10

# Supported proxy types
PROXY_TYPES="http,https,socks5,socks4"

# Max threads for page loading
MAX_THREADS=10
```

3. Add user agents to `agents.lst` (one per line)

## 🖥️ Usage

### Basic Command
```bash
python -m src "https://example.com"
```

### Command-line Options
```bash
Usage: python -m src [OPTIONS] URL

Options:
  --help                 Show this help message and exit
  --threads INTEGER      Number of threads to use (default: 10)
  --timeout INTEGER      Timeout for proxy validation in seconds (default: 10)
  --output FILE          Output file for results (default: results.json)
  --no-validate          Skip proxy validation (use with caution)
  --rotate-user-agents   Rotate user agents for each request
  --verbose              Enable verbose output for debugging
```

### Examples

#### Basic page load
```bash
python -m src "https://example.com"
```

#### Load with custom thread count
```bash
python -m src "https://example.com" --threads 20
```

#### Save results to file
```bash
python -m src "https://example.com" --output results.json
```

#### Skip proxy validation
```bash
python -m src "https://example.com" --no-validate
```

#### Rotate user agents
```bash
python -m src "https://example.com" --rotate-user-agents
```

### Example Output
```
✅ [1/10] Proxy validated: ***********:8080 (HTTP, High anonymity)
⏳ [2/10] Testing proxy: ********:3128 (SOCKS5)
❌ [3/10] Proxy failed: **********:8080 (Timeout)

🌐 Loading page: https://example.com
🔁 Using proxy: ***********:8080
✅ Success! Status: 200 | Load time: 1.23s
📊 Results saved to results.json
```

### Workflow
1. The tool scrapes proxies from configured sources
2. Validates proxies using multi-threading
3. Loads the target URL using validated proxies
4. Saves results to specified output file

## 📂 Project Structure
```
web-loader-cli/
├── src/
│   ├── cli.py            # Command-line interface
│   ├── config.py         # Configuration settings
│   ├── page_loader.py    # Page loading functionality
│   ├── proxy_manager.py  # Proxy management
│   └── __main__.py       # Entry point
├── agents.lst            # User agents list
├── proxies.lst           # Validated proxies cache
├── .env.example          # Environment configuration example
├── requirements.txt      # Dependencies
└── README.md             # This file
```

## 🤝 Contributing
Contributions are welcome! Please open an issue or submit a pull request.

## 📜 License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
