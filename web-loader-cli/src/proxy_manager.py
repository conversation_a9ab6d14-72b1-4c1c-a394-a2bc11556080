import asyncio
import time
import os
from datetime import datetime, timedelta

import httpx
import requests
from bs4 import BeautifulSoup
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn

from src.config import settings

console = Console()

async def get_real_ip(client: httpx.AsyncClient) -> str | None:
    """Gets the real public IP address of the machine."""
    try:
        response = await client.get("https://api.ipify.org?format=json", timeout=10)
        response.raise_for_status()
        return response.json()["ip"]
    except (httpx.RequestError, KeyError):
        return None

def scrape_free_proxy_list(url: str) -> set[str]:
    """
    Scrapes proxies from a table-based website like free-proxy-list.net.
    Returns a set of proxies in '{ip}:{port}' format.
    """
    proxies = set()
    try:
        response = requests.get(url, timeout=15)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, "html.parser")
        proxy_table = soup.find("table")
        if not proxy_table:
            return proxies
            
        for row in proxy_table.find("tbody").find_all("tr"):
            cells = row.find_all("td")
            if len(cells) >= 2:
                ip = cells[0].text.strip()
                port = cells[1].text.strip()
                # Basic validation for IP/port format
                if ip and port.isdigit():
                    proxies.add(f"{ip}:{port}")
    except requests.RequestException as e:
        console.print(f"❌ [bold red]Error scraping {url}:[/bold red] {e}")
    return proxies

async def test_proxy(proxy: str, client: httpx.AsyncClient, real_ip: str) -> str | None:
    """
    Tests a single proxy for responsiveness and anonymity.
    Returns the proxy string with type prefix if valid, otherwise None.
    """
    try:
        # Parse proxy string to extract type if present
        if ':' in proxy:
            parts = proxy.split(':', 1)
            ptype = parts[0].lower()
            proxy_addr = parts[1]
            
            # Validate proxy type
            if ptype not in settings.proxy_types:
                return None
        else:
            # Default to http if no type specified
            ptype = "http"
            proxy_addr = proxy
            
        # Format proxy URL based on type
        if ptype in ["http", "https"]:
            proxy_url = f"{ptype}://{proxy_addr}"
            proxies = {"http://": proxy_url, "https://": proxy_url}
        elif ptype == "socks4":
            proxy_url = f"socks4://{proxy_addr}"
            proxies = {"http://": proxy_url, "https://": proxy_url}
        elif ptype == "socks5":
            proxy_url = f"socks5://{proxy_addr}"
            proxies = {"http://": proxy_url, "https://": proxy_url}
        else:
            return None
        
        # We test against a URL that echoes our request info
        test_url = "https://httpbin.org/anything"
        
        response = await client.get(test_url, proxies=proxies, timeout=settings.proxy_test_timeout)
        response.raise_for_status()
        
        data = response.json()
        origin_ip = data.get("origin", "").split(',')[0]
        headers = data.get("headers", {})

        # Anonymity Check
        is_anonymous = origin_ip == proxy_addr.split(':')[0] and real_ip not in str(headers)

        if is_anonymous:
            return f"{ptype}:{proxy_addr}"
            
    except (httpx.RequestError, httpx.TimeoutException, ValueError):
        # This includes proxy errors, connection timeouts, read timeouts, etc.
        pass
    return None

def run_proxy_validation():
    """
    Orchestrates the full proxy scraping and validation process.
    """
    if settings.enable_cache:
        if os.path.exists(settings.proxies_file):
            file_mod_time = datetime.fromtimestamp(os.path.getmtime(settings.proxies_file))
            if datetime.now() - file_mod_time < timedelta(minutes=settings.cache_duration_minutes):
                console.print("✅ [bold green]Using recent cached proxy list.[/bold green]")
                return

    if not settings.enable_proxy_scraping:
        console.print("ℹ️ [yellow]Proxy scraping is disabled in settings.[/yellow]")
        if not os.path.exists(settings.proxies_file):
             console.print("❌ [bold red]Error:[/bold red] No `proxies.lst` found and scraping is disabled.")
             exit(1)
        return

    console.print("\n[bold cyan]Step 1: Retrieving and Validating Proxies[/bold cyan]")
    
    with console.status("[bold yellow]⚙️ Scraping proxy sources...[/bold yellow]", spinner="dots"):
        scraped_proxies = set()
        for url in settings.proxy_urls:
            scraped_proxies.update(scrape_free_proxy_list(url))
    
    if not scraped_proxies:
        console.print("❌ [bold red]No proxies were scraped. Exiting.[/bold red]")
        exit(1)
        
    console.print(f"🔎 Found [bold magenta]{len(scraped_proxies)}[/bold magenta] potential proxies. Now testing...")

    async def main_test():
        async with httpx.AsyncClient() as client:
            real_ip = await get_real_ip(client)
            if not real_ip:
                console.print("❌ [bold red]Could not determine real IP. Cannot perform anonymity checks.[/bold red]")
                return []

            tasks = [test_proxy(p, client, real_ip) for p in scraped_proxies]
            
            validated_proxies = []
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TimeElapsedColumn(),
                console=console,
            ) as progress:
                task_id = progress.add_task("[green]Validating proxies...", total=len(tasks))
                for future in asyncio.ascompleted(tasks):
                    result = await future
                    if result:
                        validated_proxies.append(result)
                    progress.update(task_id, advance=1)
            
            return validated_proxies

    validated_proxies = asyncio.run(main_test())

    if validated_proxies:
        try:
            with open(settings.proxies_file, "w") as f:
                for proxy in validated_proxies:
                    f.write(f"{proxy}\n")
            # Store scraped count for later reporting
            with open(settings.proxies_file + ".meta", "w") as meta:
                meta.write(f"scraped={len(scraped_proxies)}\n")
                meta.write(f"validated={len(validated_proxies)}\n")
            console.print(f"✅ [bold green]Success![/bold green] Saved [bold magenta]{len(validated_proxies)}[/bold magenta] working anonymous proxies to `proxies.lst`.")
        except IOError as e:
            console.print(f"❌ [bold red]Error saving proxy list:[/bold red] {e}")
    else:
        console.print("❌ [bold red]No working anonymous proxies were found.[/bold red]")
