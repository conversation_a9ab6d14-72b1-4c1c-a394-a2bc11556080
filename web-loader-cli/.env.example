# ----------------------------------
# -- General Application Settings --
# ----------------------------------
# Enable/disable proxy scraping (Step 1). If false, relies on an existing proxies.lst file.
ENABLE_PROXY_SCRAPING=True

# Enable/disable caching. If true, proxy scraping is skipped if proxies.lst is recent.
ENABLE_CACHE=True
# Cache validity in minutes.
CACHE_DURATION_MINUTES=60

# ----------------------------------
# -- Proxy Manager Settings (Step 1) --
# ----------------------------------
# Comma-separated list of URLs to scrape for proxies.
PROXY_URLS="https://free-proxy-list.net/,https://spys.one,https://openproxy.space,https://proxyscrape.com/free-proxy-list,https://sslproxies.org,https://us-proxy.org,https://socks-proxy.net,https://hidemy.name/en/proxy-list/,https://geonode.com/free-proxy-list,https://free-proxy.cz/en/,https://gologin.com/free-proxy/,https://proxyway.com/best/free-proxy-list,https://www.guru99.com/free-proxy-server-list_html"



# Timeout in seconds for testing the validity and anonymity of a single proxy.
PROXY_TEST_TIMEOUT=10

# Comma-separated list of proxy types to support (http, https, socks4, socks5)
PROXY_TYPES="http,https,socks5,socks4"

# ----------------------------------
# -- Page Loader Settings (Step 2) --
# ----------------------------------
# Number of simultaneous threads to use for loading the web page.
MAX_THREADS=10
