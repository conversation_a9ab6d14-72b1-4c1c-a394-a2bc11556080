import argparse
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from src.config import settings
from src.proxy_manager import run_proxy_validation
from src.page_loader import run_loading_process

console = Console()

def main():
    """
    Main entry point for the CLI application.
    """
    parser = argparse.ArgumentParser(
        description="A CLI tool to load web pages using a list of validated proxies.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("url", help="The full URL of the web page to load (e.g., https://example.com).")
    args = parser.parse_args()

    # --- Display Header ---
    header = Text("Web Page Loader CLI", justify="center", style="bold magenta")
    console.print(Panel(header, border_style="blue"))
    
    # --- Step 1: Proxy Validation ---
    try:
        run_proxy_validation()
    except Exception as e:
        console.print(f"❌ [bold red]A critical error occurred during proxy validation:[/bold red] {e}")
        return

    # --- Step 2: Page Loading ---
    try:
        run_loading_process(args.url)
    except Exception as e:
        console.print(f"❌ [bold red]A critical error occurred during page loading:[/bold red] {e}")

if __name__ == "__main__":
    main()
