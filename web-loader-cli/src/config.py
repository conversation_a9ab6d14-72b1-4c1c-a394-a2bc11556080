import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    """
    Loads and holds all configuration settings from the .env file.
    """
    def __init__(self):
        # Application settings
        self.enable_proxy_scraping: bool = os.getenv("ENABLE_PROXY_SCRAPING", "True").lower() == "true"
        self.enable_cache: bool = os.getenv("ENABLE_CACHE", "True").lower() == "true"
        self.cache_duration_minutes: int = int(os.getenv("CACHE_DURATION_MINUTES", "60"))

        # Proxy manager settings
        self.proxy_urls: list[str] = [url.strip() for url in os.getenv("PROXY_URLS", "https://free-proxy-list.net/").split(',')]
        self.proxy_test_timeout: int = int(os.getenv("PROXY_TEST_TIMEOUT", "10"))
        self.proxy_types: list[str] = [ptype.strip().lower() for ptype in os.getenv("PROXY_TYPES", "http,https,socks5,socks4").split(',')]
        
        # Page loader settings
        self.max_threads: int = int(os.getenv("MAX_THREADS", "10"))

        # File paths
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.agents_file: str = os.path.join(self.base_dir, "agents.lst")
        self.proxies_file: str = os.path.join(self.base_dir, "proxies.lst")

# Create a single instance of settings to be imported by other modules
settings = Settings()
