import random
import os
from concurrent.futures import Thread<PERSON><PERSON>Executor, as_completed

import requests
from rich.console import Console
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn

from src.config import settings

console = Console()

def load_user_agents() -> list[str]:
    """Loads user agents from the agents.lst file."""
    if not os.path.exists(settings.agents_file):
        console.print(f"❌ [bold red]Error:[/bold red] `agents.lst` file not found.")
        return []
    try:
        with open(settings.agents_file, "r") as f:
            agents = [line.strip() for line in f if line.strip()]
        if not agents:
            console.print("⚠️ [yellow]Warning:[/yellow] `agents.lst` is empty.")
        return agents
    except IOError as e:
        console.print(f"❌ [bold red]Error reading agents file:[/bold red] {e}")
        return []

def load_page_with_proxy(target_url: str, proxy: str, user_agent: str) -> tuple[str, bool, str]:
    """
    Loads a web page using a specific proxy and user agent.
    Returns a tuple of (proxy, success_status, message).
    """
    try:
        # Parse proxy string to extract type if present
        if ':' in proxy:
            parts = proxy.split(':', 1)
            ptype = parts[0].lower()
            proxy_addr = parts[1]
        else:
            # Default to http if no type specified
            ptype = "http"
            proxy_addr = proxy
            
        # Format proxy URL based on type
        if ptype in ["http", "https"]:
            proxy_url = f"{ptype}://{proxy_addr}"
            proxies = {"http": proxy_url, "https": proxy_url}
        elif ptype == "socks4":
            proxy_url = f"socks4://{proxy_addr}"
            proxies = {"http": proxy_url, "https": proxy_url}
        elif ptype == "socks5":
            proxy_url = f"socks5://{proxy_addr}"
            proxies = {"http": proxy_url, "https": proxy_url}
        else:
            # Unsupported proxy type
            return (proxy, False, f"Failed (Unsupported proxy type: {ptype})")
        
        headers = {"User-Agent": user_agent}
        response = requests.get(target_url, headers=headers, proxies=proxies, timeout=20)
        if 200 <= response.status_code < 300:
            return (proxy, True, f"Success (Status: {response.status_code})")
        else:
            return (proxy, False, f"Failed (Status: {response.status_code})")
    except requests.exceptions.ProxyError:
        return (proxy, False, "Failed (Proxy Error)")
    except requests.exceptions.Timeout:
        return (proxy, False, "Failed (Timeout)")
    except requests.exceptions.RequestException as e:
        return (proxy, False, f"Failed (Request Error: {type(e).__name__})")

def run_loading_process(target_url: str):
    """
    Orchestrates the threaded process of loading a URL with all validated proxies.
    """
    console.print(f"\n[bold cyan]Step 2: Loading Web Page with Proxies[/bold cyan]")
    console.print(f"🎯 [bold]Target URL:[/bold] {target_url}")

    if not os.path.exists(settings.proxies_file):
        console.print("❌ [bold red]Error:[/bold red] `proxies.lst` not found. Run with proxy scraping enabled first.")
        return

    # Try to load proxy statistics if available
    scraped_count = "unknown"
    validated_count = "unknown"
    meta_file = settings.proxies_file + ".meta"
    if os.path.exists(meta_file):
        try:
            with open(meta_file, "r") as mf:
                for line in mf:
                    if line.startswith("scraped="):
                        scraped_count = line.split("=")[1].strip()
                    elif line.startswith("validated="):
                        validated_count = line.split("=")[1].strip()
        except Exception:
            pass

    with open(settings.proxies_file, "r") as f:
        proxies = [line.strip() for line in f if line.strip()]
    
    user_agents = load_user_agents()

    if not proxies or not user_agents:
        console.print("❌ [bold red]Cannot proceed without proxies and user agents.[/bold red]")
        return
        
    console.print(f"📊 Proxies: [magenta]{scraped_count}[/magenta] scraped, [green]{validated_count}[/green] validated, [blue]{len(proxies)}[/blue] to be used")
    console.print(f"🚀 Starting load process with [cyan]{settings.max_threads}[/cyan] threads")

    success_count = 0
    fail_count = 0

    with Progress(
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TextColumn("([green]{task.completed} successful[/green] / [red]{task.fields[fails]} failed[/red])"),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        task = progress.add_task("[cyan]Loading page...", total=len(proxies), fails=0)

        with ThreadPoolExecutor(max_workers=settings.max_threads) as executor:
            futures = {
                executor.submit(load_page_with_proxy, target_url, proxy, random.choice(user_agents))
                for proxy in proxies
            }
            
            for future in as_completed(futures):
                proxy, success, message = future.result()
                if success:
                    success_count += 1
                    console.log(f"✅ [green]{proxy:<21}[/green] -> {message}")
                else:
                    fail_count += 1
                    console.log(f"❌ [red]{proxy:<21}[/red] -> {message}")
                
                progress.update(task, advance=1, fails=fail_count)
    
    console.print("\n[bold blue]--- Loading Process Complete ---[/bold blue]")
    console.print(f"Total Proxies Used: [bold]{len(proxies)}[/bold]")
    console.print(f"✅ Successful Loads: [bold green]{success_count}[/bold green]")
    console.print(f"❌ Failed Loads: [bold red]{fail_count}[/bold red]")
